#!/bin/bash

echo "🔧 Fixing Git Remote for EHIGOLD Portfolio..."

cd static-export

echo "📋 Current remotes:"
git remote -v

echo ""
echo "🔄 Removing existing origin..."
git remote remove origin

echo ""
echo "➕ Please enter your GitHub repository URL:"
echo "Example: https://github.com/yourusername/ehigold-portfolio.git"
read -p "Repository URL: " repo_url

echo ""
echo "🔗 Adding new remote..."
git remote add origin "$repo_url"

echo ""
echo "🚀 Pushing to GitHub..."
git push -u origin main

echo ""
echo "✅ Done! Your portfolio is now on GitHub"
echo "🌐 Next: Go to vercel.com and import this repository"
