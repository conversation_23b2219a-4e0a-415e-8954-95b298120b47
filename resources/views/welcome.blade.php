@section('title', 'EHIGOLD')
@include('layouts.head')
@include('layouts.navigation')







  <!-- Hero Section -->

<section class="relative z-10 pt-20 pb-10 bg-gradient-to-r from-black to-gray-900 text-white overflow-hidden">
  <!-- Floating Geometric Shapes -->
  <div class="absolute inset-0 z-0 pointer-events-none">
    <!-- Floating Circle -->
    <div class="absolute top-10 left-10 w-10 h-10 bg-[#23C7AC] opacity-20 rounded-full animate-float-slow"></div>

    <!-- Floating Square -->
    <div class="absolute bottom-10 right-20 w-8 h-8 bg-[#23C7AC] opacity-20 rotate-12 animate-float-fast"></div>

    <!-- Floating Triangle (SVG) -->
    <svg class="absolute top-1/3 right-1/3 w-12 h-12 opacity-20 animate-float-medium" viewBox="0 0 100 100" fill="#23C7AC">
      <polygon points="50,0 0,100 100,100" />
    </svg>
  </div>

  <!-- Hero Content -->
  <div class="relative container mx-auto flex flex-col md:flex-row items-center md:items-start gap-10 px-6 z-10">
    <!-- Profile Image -->
    <div class="flex-shrink-0">
      <img src="{{ asset('images/ehigoldd.jpg') }}"
           alt="Profile Picture"
           class="w-56 h-56 rounded-2xl shadow-lg border-4 border-white object-cover" />
    </div>

    <!-- Bio & Text -->
    <div class="flex flex-col justify-center h-full mt-10 md:mt-0 space-y-6">
      <h1 class="text-4xl lg:text-6xl font-bold leading-tight">
        Hi, I'm <span class="text-[#23C7AC]">EHIGOLD</span>
      </h1>

      <p class="text-lg lg:text-2xl max-w-xl">
        I design logos and flyers, teach web design, and develop fullstack web applications.
      </p>

      <!-- Spinner -->
      <div class="animate-spin rounded-full h-8 w-8 border-t-4 border-[#23C7AC]"></div>

      <!-- Scroll Down -->
      <div class="mt-6">
        <a href="#overview" class="animate-bounce text-gray-400 inline-flex items-center gap-2">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
          <span class="text-sm">Scroll down</span>
        </a>
      </div>
    </div>
  </div>
</section>


</style>




<!-- Overview -->
<section id="overview" class="py-20 px-6 lg:px-32 bg-gray-900 text-white">
  <div class="max-w-6xl mx-auto">
    <h2 class="text-4xl font-bold mb-4">Overview</h2>
    <p class="text-gray-300 text-lg mb-10 leading-relaxed">
     I’m a creative Graphics and Web Designer with over 5 years of experience delivering visually
      compelling and user-focused designs. With an additional year of hands-on experience as a
      Web Developer, I bridge the gap between design and functionality — building responsive, 
      full-featured websites and applications that not only look great but also perform flawlessly.    </p>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8">
      <!-- Card 1 -->
      <div data-aos="fade-up" data-aos-delay="100" class="h-72 bg-gray-800 border border-[#23C7AC] hover:border-[#23C7AC] hover:bg-gray-700 transition duration-300 rounded-2xl p-6 shadow-md text-center flex flex-col items-center justify-center">
        <div class="text-[#23C7AC] mb-4">
          <!-- Icon: Paint Brush for Graphics -->
          <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232a2.828 2.828 0 114 4L7 21H3v-4L15.232 5.232z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold">Graphics Designer</h3>
      </div>

      <!-- Card 2 -->
      <div data-aos="fade-up" data-aos-delay="200" class="h-72 bg-gray-800 border border-[#23C7AC] hover:border-[#23C7AC] hover:bg-gray-700 transition duration-300 rounded-2xl p-6 shadow-md text-center flex flex-col items-center justify-center">
        <div class="text-[#23C7AC] mb-4">
          <!-- Icon: Layout for Web Design -->
          <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v3H3V4zm0 5h18v11a1 1 0 01-1 1H4a1 1 0 01-1-1V9z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold">Web Designer</h3>
      </div>

      <!-- Card 3 -->
      <div data-aos="fade-up" data-aos-delay="300" class="h-72 bg-gray-800 border border-[#23C7AC] hover:border-[#23C7AC] hover:bg-gray-700 transition duration-300 rounded-2xl p-6 shadow-md text-center flex flex-col items-center justify-center">
        <div class="text-[#23C7AC] mb-4">
          <!-- Icon: Terminal/Code for Backend Developer -->
          <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l-3 3 3 3m5-6h3m4 6H5a2 2 0 01-2-2V7a2 2 0 012-2h16a2 2 0 012 2v8a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold">Web Developer</h3>
      </div>
       <!-- Card 4 -->
      <div data-aos="fade-up" data-aos-delay="300" class="h-72 bg-gray-800 border border-[#23C7AC] hover:border-[#23C7AC] hover:bg-gray-700 transition duration-300 rounded-2xl p-6 shadow-md text-center flex flex-col items-center justify-center">
        <div class="text-[#23C7AC] mb-4">
          <!-- Icon: Terminal/Code for Backend Developer -->
          <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l-3 3 3 3m5-6h3m4 6H5a2 2 0 01-2-2V7a2 2 0 012-2h16a2 2 0 012 2v8a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold">Web Design Tutor</h3>
      </div>
    </div>
  </div>
</section>




  <!-- Work Experience Timeline -->
  <section id="experience" class="py-16 px-8 lg:px-32">
    <h2 class="text-3xl font-bold mb-8">Work Experience</h2>
    <ol class="relative border-l border-gray-600">
      <!-- Timeline item example -->
      <li class="mb-10 ml-6">
        <span class="absolute -left-3 flex items-center justify-center w-6 h-6 bg-[#23C7AC] rounded-full ring-8 ring-gray-800">
          <!-- timeline icon -->
        </span>
        <h3 class="text-lg font-semibold">Web Developer/ Wordpress Tutor @ StormCell</h3>
        {{-- <time class="block mb-2 text-sm">Jan 2024 - Present</time> --}}
        <p class="text-gray-300"><span class=" text-[#23C7AC] font-bold text-xl p-1">-</span>Developing and maintaining web applications using Laravel, Flowbite, Tailwind Css and other related technologies.</p>
        <p><span class=" text-[#23C7AC] font-bold text-xl p-1">-</span>Collaborating with cross-functional teams including designers, product managers, and other developers to create high-quality products.</p>
        </p><span class=" text-[#23C7AC] font-bold text-xl p-1">-</span>Implementing responsive design principles to ensure applications are accessible on various devices.</p>
        <p><span class=" text-[#23C7AC] font-bold text-xl p-1">-</span>Worked closely with the UX team to implement a responsive design and ensure a consistent user experience across all platforms.</p>
      </li>
      <!-- Repeat for other roles... -->
      <li class="mb-10 ml-6">
        <span class="absolute -left-3 flex items-center justify-center w-6 h-6 bg-[#23C7AC] rounded-full ring-8 ring-gray-800">
          <!-- timeline icon -->
        </span>
        <h3 class="text-lg font-semibold">Backend Developer / Wordpress Developer @ Eachblock</h3>
        {{-- <time class="block mb-2 text-sm">Jan 2024 - Present</time> --}}
        <p class="text-gray-300"><span class=" text-[#23C7AC] font-bold text-xl p-1">-</span>Developing and maintaining web applications using Laravel.</p>
        <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Designing and managing APIs.</p>
       <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Writing clean, scalable, and secure backend code using frameworks like Laravel.</p>
       <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Creating and managing databases, queries, and relationships like MySQL.</p>
      <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Integrating third-party services like payment gateways, authentication APIs etc.</p>
      <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Building functional and responsive websites using wordpress.</p>
       <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Collaborating with frontend developers to deliver complete, functional features.</p>

      </li>
       <!-- Repeat for other roles... -->
      <li class="mb-10 ml-6">
        <span class="absolute -left-3 flex items-center justify-center w-6 h-6 bg-[#23C7AC] rounded-full ring-8 ring-gray-800">
          <!-- timeline icon -->
        </span>
        <h3 class="text-lg font-semibold">Web Developer @ Ielite</h3>
        {{-- <time class="block mb-2 text-sm">Jan 2024 - Present</time> --}}
              <p class="text-gray-300">
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Contributed to the development of a logistics web platform by extending and maintaining existing Laravel-based features.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Implemented new cargo management logic, improving efficiency in shipment tracking and rate calculations.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Integrated third-party APIs for external cargo bookings, enhancing system automation and service reliability.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Built simple and interative frontend designs.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Ensured backend logic was optimized for scalability, data accuracy, and real-time updates.
      </p>

        </li>
       <!-- Repeat for other roles... -->
      <li class="mb-10 ml-6">
        <span class="absolute -left-3 flex items-center justify-center w-6 h-6 bg-[#23C7AC] rounded-full ring-8 ring-gray-800">
          <!-- timeline icon -->
        </span>
        <h3 class="text-lg font-semibold">Website Manager @ Lashi Beauty</h3>
        {{-- <time class="block mb-2 text-sm">Jan 2024 - Present</time> --}}
<p class="text-gray-300">
  <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
  Managing a WordPress-based e-commerce website by checking for errors, performing regular updates.
</p>
<p class="text-gray-300">
  <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
  Uploading of new products.
</p>
      </li>
    </ol>
  </section>



 



  <!-- Portfolio Section -->
  <section class="py-16 px-6">
    <h2 class="text-center text-2xl font-bold mb-8">Portfolio</h2>

    <!-- Tabs -->
    <div class="mb-6 text-center">
      <ul class="flex justify-center space-x-4" id="tabs" data-tabs-toggle="#tabsContent" role="tablist">
        <li>
          <button id="apps-tab" data-tabs-target="#apps" type="button" role="tab" class="px-4 py-2 text-white bg-[#23C7AC] rounded hover:bg-gray-50">WEBSITES</button>
        </li>
        <li>
          <button id="designs-tab" data-tabs-target="#designs" type="button" role="tab" class="px-4 py-2 text-white bg-gray-700 rounded hover:bg-gray-600">GRAPHICS</button>
        </li>
        <li>
          <button id="logos-tab" data-tabs-target="#logos" type="button" role="tab" class="px-4 py-2 text-white bg-gray-700 rounded hover:bg-gray-600">LOGOS</button>
        </li>
      </ul>
    </div>

    <!-- Tabs Content -->
    <div id="tabsContent">
      <div class="p-4 mx-auto max-w-7xl">
      <div id="apps" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" role="tabpanel" aria-labelledby="apps-tab">
          @foreach ([
            ['title' => 'Peterfemassociates', 'image' => 'peterfem.png', 'url' => 'https://peterfemassociates.com'],
            ['title' => 'Haemtechnico Technico', 'image' => 'haemtechnico.png', 'url' => 'https://haemtechnico.com'],
            ['title' => 'Beberrieslounge', 'image' => 'beberrieslounge.png', 'url' => 'https://beberrieslounge.com'],
            ['title' => 'Jetsettlers Services', 'image' => 'jetsettlersservices.png', 'url' => 'https://jetsettlersservicesinc.ca'],

          ] as $site)
          <a href="{{ $site['url'] }}" target="_blank" class="block bg-gray-800 rounded-xl shadow hover:scale-[1.02] transition">
            <img src="{{ asset('images/' . $site['image']) }}" alt="{{ $site['title'] }}" class="w-full h-48 object-cover rounded-t-lg">
            <div class="p-4">
              <h4 class="font-bold text-lg">{{ $site['title'] }}</h4>
            </div>
          </a>
          @endforeach
      </div>
    </div>


        <div id="designs" class="hidden p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" role="tabpanel" aria-labelledby="designs-tab">
        @foreach (['design1.jpg', 'design2.jpg', 'design5.jpg', 'design6.jpg', 'design7.jpg'] as $design)
        <div class="bg-gray-800 rounded-lg shadow overflow-hidden h-156"> {{-- h-96 for taller card --}}
            <img src="{{ asset('images/designs/' . $design) }}" alt="Design" class="w-full h-full object-cover">
        </div>
        @endforeach
    </div>

     <div id="logos" class="hidden p-4 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6" role="tabpanel" aria-labelledby="designs-tab">
    @foreach (['design3.jpg', 'design4.jpg'] as $design)
    <div class="bg-gray-800 rounded-lg shadow overflow-hidden h-80">
        <img src="{{ asset('images/logos/' . $design) }}" 
             alt="Design" 
             class="w-full h-full object-contain p-4" />
    </div>
    @endforeach
</div>


    </div>
  </section>







<x-footer-main />
