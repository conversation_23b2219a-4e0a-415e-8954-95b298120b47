@section('title', 'EHIGOLD')
@include('layouts.head')
@include('layouts.navigation')

<style>
  @keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-10px) rotate(5deg); }
  }

  @keyframes float-fast {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(-5deg); }
  }

  @keyframes float-medium {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-15px) rotate(3deg); }
  }

  @keyframes gradient-shift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
  }

  @keyframes glow-pulse {
    0%, 100% { box-shadow: 0 0 20px rgba(35, 199, 172, 0.3); }
    50% { box-shadow: 0 0 40px rgba(35, 199, 172, 0.6); }
  }

  @keyframes slide-in-up {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-float-slow {
    animation: float-slow 6s ease-in-out infinite;
  }

  .animate-float-fast {
    animation: float-fast 3s ease-in-out infinite;
  }

  .animate-float-medium {
    animation: float-medium 4s ease-in-out infinite;
  }

  .animate-gradient {
    background-size: 200% 200%;
    animation: gradient-shift 3s ease infinite;
  }

  .animate-glow {
    animation: glow-pulse 2s ease-in-out infinite;
  }

  .animate-slide-up {
    animation: slide-in-up 0.6s ease-out;
  }

  /* Smooth scrolling */
  html {
    scroll-behavior: smooth;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
  }

  ::-webkit-scrollbar-track {
    background: #1f2937;
  }

  ::-webkit-scrollbar-thumb {
    background: #23C7AC;
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: #1ea892;
  }

  /* Tab switching animations */
  .tab-content {
    opacity: 0;
    transform: translateY(20px);
    transition: all 0.3s ease-in-out;
  }

  .tab-content.active {
    opacity: 1;
    transform: translateY(0);
  }

  /* Hover effects for cards */
  .card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .card-hover:hover {
    transform: translateY(-8px) scale(1.02);
  }

  /* Loading animation for images */
  .image-loading {
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
  }

  @keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  /* Mobile responsive improvements */
  @media (max-width: 640px) {
    .hero-text {
      font-size: clamp(2rem, 8vw, 3rem);
    }

    .section-padding {
      padding-left: 1rem;
      padding-right: 1rem;
    }
  }
</style>







  <!-- Hero Section -->
<section class="relative z-10 pt-16 pb-16 md:pt-20 md:pb-20 bg-gradient-to-br from-black via-gray-900 to-gray-800 text-white overflow-hidden min-h-screen flex items-center">
  <!-- Enhanced Floating Geometric Shapes -->
  <div class="absolute inset-0 z-0 pointer-events-none">
    <!-- Floating Circle -->
    <div class="absolute top-10 left-4 md:left-10 w-6 h-6 md:w-10 md:h-10 bg-[#23C7AC] opacity-20 rounded-full animate-pulse"></div>

    <!-- Floating Square -->
    <div class="absolute bottom-20 right-4 md:right-20 w-4 h-4 md:w-8 md:h-8 bg-[#23C7AC] opacity-20 rotate-12 animate-bounce"></div>

    <!-- Additional floating elements for visual appeal -->
    <div class="absolute top-1/4 left-1/4 w-3 h-3 md:w-6 md:h-6 bg-[#23C7AC] opacity-10 rounded-full animate-ping"></div>
    <div class="absolute bottom-1/3 left-1/3 w-2 h-2 md:w-4 md:h-4 bg-white opacity-10 rounded-full animate-pulse"></div>

    <!-- Floating Triangle (SVG) -->
    <svg class="absolute top-1/3 right-1/4 md:right-1/3 w-8 h-8 md:w-12 md:h-12 opacity-20 animate-pulse" viewBox="0 0 100 100" fill="#23C7AC">
      <polygon points="50,0 0,100 100,100" />
    </svg>
  </div>

  <!-- Hero Content -->
  <div class="relative container mx-auto flex flex-col md:flex-row items-center md:items-center gap-8 md:gap-12 px-4 md:px-6 z-10 w-full">
    <!-- Profile Image -->
    <div class="flex-shrink-0 order-1 md:order-1">
      <div class="relative">
        <img src="{{ asset('images/ehigoldd.jpg') }}"
             alt="Profile Picture"
             class="w-48 h-48 md:w-64 md:h-64 lg:w-72 lg:h-72 rounded-3xl shadow-2xl border-4 border-[#23C7AC] object-cover transform hover:scale-105 transition-transform duration-300" />
        <!-- Glow effect -->
        <div class="absolute inset-0 rounded-3xl bg-[#23C7AC] opacity-20 blur-xl -z-10"></div>
      </div>
    </div>

    <!-- Bio & Text -->
    <div class="flex flex-col justify-center text-center md:text-left order-2 md:order-2 space-y-4 md:space-y-6 max-w-2xl">
      <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight">
        Hi, I'm <span class="text-[#23C7AC] bg-gradient-to-r from-[#23C7AC] to-[#1ea892] bg-clip-text text-transparent">EHIGOLD</span>
      </h1>

      <p class="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 leading-relaxed max-w-xl mx-auto md:mx-0">
        I design stunning logos and flyers, teach web design, and develop fullstack web applications that bring ideas to life.
      </p>

      <!-- Enhanced CTA Buttons -->
      <div class="flex flex-col sm:flex-row gap-4 mt-6 md:mt-8">
        <a href="#portfolio" class="bg-[#23C7AC] hover:bg-[#1ea892] text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
          View My Work
        </a>
        <a href="#contact" class="border-2 border-[#23C7AC] text-[#23C7AC] hover:bg-[#23C7AC] hover:text-white px-6 py-3 rounded-full font-semibold transition-all duration-300">
          Get In Touch
        </a>
      </div>

      <!-- Scroll Down -->
      <div class="mt-8 md:mt-12">
        <a href="#overview" class="animate-bounce text-gray-400 hover:text-[#23C7AC] inline-flex items-center gap-2 transition-colors duration-300">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 md:h-8 md:w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
          </svg>
          <span class="text-sm md:text-base">Scroll down</span>
        </a>
      </div>
    </div>
  </div>
</section>


</style>




<!-- Overview -->
<section id="overview" class="py-16 md:py-24 px-4 md:px-6 lg:px-32 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white">
  <div class="max-w-7xl mx-auto">
    <div class="text-center mb-12 md:mb-16">
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Overview</h2>
      <p class="text-gray-300 text-base md:text-lg lg:text-xl mb-10 leading-relaxed max-w-4xl mx-auto">
        I’m a creative Graphics and Web Designer with over 5 years of experience delivering visually
        compelling and user-focused designs. With an additional year of hands-on experience as a
        Web Developer, I bridge the gap between design and functionality — building responsive,
        full-featured websites and applications that not only look great but also perform flawlessly.
      </p>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
      <!-- Card 1 -->
      <div data-aos="fade-up" data-aos-delay="100" class="group h-64 md:h-72 bg-gradient-to-br from-gray-800 to-gray-900 border border-[#23C7AC]/30 hover:border-[#23C7AC] hover:bg-gradient-to-br hover:from-gray-700 hover:to-gray-800 transition-all duration-500 rounded-2xl p-6 shadow-lg hover:shadow-2xl text-center flex flex-col items-center justify-center transform hover:-translate-y-2">
        <div class="text-[#23C7AC] mb-4 group-hover:scale-110 transition-transform duration-300">
          <!-- Icon: Paint Brush for Graphics -->
          <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-[#23C7AC] to-[#1ea892] rounded-2xl flex items-center justify-center mx-auto shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 md:w-10 md:h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232a2.828 2.828 0 114 4L7 21H3v-4L15.232 5.232z" />
            </svg>
          </div>
        </div>
        <h3 class="text-xl md:text-2xl font-bold text-white group-hover:text-[#23C7AC] transition-colors duration-300">Graphics Designer</h3>
        <p class="text-gray-400 text-sm md:text-base mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">Creative visual solutions</p>
      </div>

      <!-- Card 2 -->
      <div data-aos="fade-up" data-aos-delay="200" class="group h-64 md:h-72 bg-gradient-to-br from-gray-800 to-gray-900 border border-[#23C7AC]/30 hover:border-[#23C7AC] hover:bg-gradient-to-br hover:from-gray-700 hover:to-gray-800 transition-all duration-500 rounded-2xl p-6 shadow-lg hover:shadow-2xl text-center flex flex-col items-center justify-center transform hover:-translate-y-2">
        <div class="text-[#23C7AC] mb-4 group-hover:scale-110 transition-transform duration-300">
          <!-- Icon: Layout for Web Design -->
          <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-[#23C7AC] to-[#1ea892] rounded-2xl flex items-center justify-center mx-auto shadow-lg">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 md:w-10 md:h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v3H3V4zm0 5h18v11a1 1 0 01-1 1H4a1 1 0 01-1-1V9z" />
            </svg>
          </div>
        </div>
        <h3 class="text-xl md:text-2xl font-bold text-white group-hover:text-[#23C7AC] transition-colors duration-300">Web Designer</h3>
        <p class="text-gray-400 text-sm md:text-base mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">User-focused interfaces</p>
      </div>

      <!-- Card 3 -->
      <div data-aos="fade-up" data-aos-delay="300" class="h-72 bg-gray-800 border border-[#23C7AC] hover:border-[#23C7AC] hover:bg-gray-700 transition duration-300 rounded-2xl p-6 shadow-md text-center flex flex-col items-center justify-center">
        <div class="text-[#23C7AC] mb-4">
          <!-- Icon: Terminal/Code for Backend Developer -->
          <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l-3 3 3 3m5-6h3m4 6H5a2 2 0 01-2-2V7a2 2 0 012-2h16a2 2 0 012 2v8a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold">Web Developer</h3>
      </div>
       <!-- Card 4 -->
      <div data-aos="fade-up" data-aos-delay="300" class="h-72 bg-gray-800 border border-[#23C7AC] hover:border-[#23C7AC] hover:bg-gray-700 transition duration-300 rounded-2xl p-6 shadow-md text-center flex flex-col items-center justify-center">
        <div class="text-[#23C7AC] mb-4">
          <!-- Icon: Terminal/Code for Backend Developer -->
          <svg xmlns="http://www.w3.org/2000/svg" class="w-12 h-12 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l-3 3 3 3m5-6h3m4 6H5a2 2 0 01-2-2V7a2 2 0 012-2h16a2 2 0 012 2v8a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 class="text-2xl font-semibold">Web Design Tutor</h3>
      </div>
    </div>
  </div>
</section>




  <!-- Work Experience Timeline -->
  <section id="experience" class="py-16 md:py-24 px-4 md:px-8 lg:px-32 bg-gradient-to-br from-gray-50 to-white">
    <div class="max-w-6xl mx-auto">
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 text-gray-800">Work Experience</h2>
        <p class="text-lg md:text-xl text-gray-600 max-w-2xl mx-auto">My professional journey and key achievements</p>
      </div>

      <div class="relative">
        <!-- Timeline line - hidden on mobile, visible on larger screens -->
        <div class="hidden md:block absolute left-8 top-0 bottom-0 w-0.5 bg-gradient-to-b from-[#23C7AC] to-gray-300"></div>

        <ol class="space-y-8 md:space-y-12">
          <!-- Timeline item 1 -->
          <li class="relative">
            <!-- Timeline dot -->
            <div class="hidden md:flex absolute left-6 w-4 h-4 bg-[#23C7AC] rounded-full ring-4 ring-white shadow-lg"></div>

            <!-- Content card -->
            <div class="md:ml-16 bg-white rounded-2xl shadow-lg hover:shadow-xl transition-all duration-300 p-6 md:p-8 border border-gray-100 hover:border-[#23C7AC]/20">
              <div class="flex flex-col md:flex-row md:items-center md:justify-between mb-4">
                <h3 class="text-xl md:text-2xl font-bold text-gray-800 mb-2 md:mb-0">Web Developer / WordPress Tutor</h3>
                <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-[#23C7AC]/10 text-[#23C7AC] border border-[#23C7AC]/20">
                  @ StormCell
                </span>
              </div>

              <div class="space-y-3">
                <div class="flex items-start gap-3">
                  <div class="w-2 h-2 bg-[#23C7AC] rounded-full mt-2 flex-shrink-0"></div>
                  <p class="text-gray-600 leading-relaxed">Developing and maintaining web applications using Laravel, Flowbite, Tailwind CSS and other related technologies.</p>
                </div>
                <div class="flex items-start gap-3">
                  <div class="w-2 h-2 bg-[#23C7AC] rounded-full mt-2 flex-shrink-0"></div>
                  <p class="text-gray-600 leading-relaxed">Collaborating with cross-functional teams including designers, product managers, and other developers to create high-quality products.</p>
                </div>
                <div class="flex items-start gap-3">
                  <div class="w-2 h-2 bg-[#23C7AC] rounded-full mt-2 flex-shrink-0"></div>
                  <p class="text-gray-600 leading-relaxed">Implementing responsive design principles to ensure applications are accessible on various devices.</p>
                </div>
                <div class="flex items-start gap-3">
                  <div class="w-2 h-2 bg-[#23C7AC] rounded-full mt-2 flex-shrink-0"></div>
                  <p class="text-gray-600 leading-relaxed">Worked closely with the UX team to implement responsive design and ensure consistent user experience across all platforms.</p>
                </div>
              </div>
            </div>
          </li>
      <!-- Repeat for other roles... -->
      <li class="mb-10 ml-6">
        <span class="absolute -left-3 flex items-center justify-center w-6 h-6 bg-[#23C7AC] rounded-full ring-8 ring-gray-800">
          <!-- timeline icon -->
        </span>
        <h3 class="text-lg font-semibold">Backend Developer / Wordpress Developer @ Eachblock</h3>
        {{-- <time class="block mb-2 text-sm">Jan 2024 - Present</time> --}}
        <p class="text-gray-300"><span class=" text-[#23C7AC] font-bold text-xl p-1">-</span>Developing and maintaining web applications using Laravel.</p>
        <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Designing and managing APIs.</p>
       <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Writing clean, scalable, and secure backend code using frameworks like Laravel.</p>
       <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Creating and managing databases, queries, and relationships like MySQL.</p>
      <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Integrating third-party services like payment gateways, authentication APIs etc.</p>
      <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Building functional and responsive websites using wordpress.</p>
       <p><span class="text-[#23C7AC] font-bold text-xl p-1">-</span>Collaborating with frontend developers to deliver complete, functional features.</p>

      </li>
       <!-- Repeat for other roles... -->
      <li class="mb-10 ml-6">
        <span class="absolute -left-3 flex items-center justify-center w-6 h-6 bg-[#23C7AC] rounded-full ring-8 ring-gray-800">
          <!-- timeline icon -->
        </span>
        <h3 class="text-lg font-semibold">Web Developer @ Ielite</h3>
        {{-- <time class="block mb-2 text-sm">Jan 2024 - Present</time> --}}
              <p class="text-gray-300">
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Contributed to the development of a logistics web platform by extending and maintaining existing Laravel-based features.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Implemented new cargo management logic, improving efficiency in shipment tracking and rate calculations.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Integrated third-party APIs for external cargo bookings, enhancing system automation and service reliability.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Built simple and interative frontend designs.
      </p>
      <p>
        <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
        Ensured backend logic was optimized for scalability, data accuracy, and real-time updates.
      </p>

        </li>
       <!-- Repeat for other roles... -->
      <li class="mb-10 ml-6">
        <span class="absolute -left-3 flex items-center justify-center w-6 h-6 bg-[#23C7AC] rounded-full ring-8 ring-gray-800">
          <!-- timeline icon -->
        </span>
        <h3 class="text-lg font-semibold">Website Manager @ Lashi Beauty</h3>
        {{-- <time class="block mb-2 text-sm">Jan 2024 - Present</time> --}}
<p class="text-gray-300">
  <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
  Managing a WordPress-based e-commerce website by checking for errors, performing regular updates.
</p>
<p class="text-gray-300">
  <span class="text-[#23C7AC] font-bold text-xl p-1">-</span>
  Uploading of new products.
</p>
      </li>
    </ol>
  </section>



 



  <!-- Portfolio Section -->
  <section id="portfolio" class="py-16 md:py-24 px-4 md:px-6 bg-gradient-to-br from-gray-900 via-gray-800 to-black">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-4 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Portfolio</h2>
        <p class="text-lg md:text-xl text-gray-400 max-w-2xl mx-auto">Explore my creative work and technical projects</p>
      </div>

      <!-- Enhanced Tabs -->
      <div class="mb-8 md:mb-12">
        <div class="flex flex-col sm:flex-row justify-center gap-2 sm:gap-4 max-w-2xl mx-auto">
          <button id="apps-tab" data-tabs-target="#apps" type="button" role="tab"
                  class="flex-1 px-6 py-3 md:px-8 md:py-4 text-sm md:text-base font-semibold text-white bg-gradient-to-r from-[#23C7AC] to-[#1ea892] rounded-xl hover:from-[#1ea892] hover:to-[#23C7AC] transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 active-tab">
            <span class="flex items-center justify-center gap-2">
              <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
              </svg>
              WEBSITES
            </span>
          </button>

          <button id="designs-tab" data-tabs-target="#designs" type="button" role="tab"
                  class="flex-1 px-6 py-3 md:px-8 md:py-4 text-sm md:text-base font-semibold text-gray-300 bg-gray-800 border border-gray-700 rounded-xl hover:bg-gray-700 hover:text-white hover:border-[#23C7AC] transition-all duration-300 inactive-tab">
            <span class="flex items-center justify-center gap-2">
              <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
              </svg>
              GRAPHICS
            </span>
          </button>

          <button id="logos-tab" data-tabs-target="#logos" type="button" role="tab"
                  class="flex-1 px-6 py-3 md:px-8 md:py-4 text-sm md:text-base font-semibold text-gray-300 bg-gray-800 border border-gray-700 rounded-xl hover:bg-gray-700 hover:text-white hover:border-[#23C7AC] transition-all duration-300 inactive-tab">
            <span class="flex items-center justify-center gap-2">
              <svg class="w-4 h-4 md:w-5 md:h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zM21 5H9a2 2 0 00-2 2v10a4 4 0 004 4h6a2 2 0 002-2V7a2 2 0 00-2-2z"></path>
              </svg>
              LOGOS
            </span>
          </button>
        </div>
      </div>

    <!-- Tabs Content -->
    <div id="tabsContent">
      <div class="p-4 mx-auto max-w-7xl">
      <div id="apps" class="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8" role="tabpanel" aria-labelledby="apps-tab">
          @foreach ([
            ['title' => 'Peterfemassociates', 'image' => 'peterfem.png', 'url' => 'https://peterfemassociates.com'],
            ['title' => 'Haemtechnico Technico', 'image' => 'haemtechnico.png', 'url' => 'https://haemtechnico.com'],
            ['title' => 'Beberrieslounge', 'image' => 'beberrieslounge.png', 'url' => 'https://beberrieslounge.com'],
            ['title' => 'Jetsettlers Services', 'image' => 'jetsettlersservices.png', 'url' => 'https://jetsettlersservicesinc.ca'],

          ] as $site)
          <a href="{{ $site['url'] }}" target="_blank" class="group block bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl shadow-lg hover:shadow-2xl hover:scale-[1.02] transition-all duration-300 overflow-hidden border border-gray-700 hover:border-[#23C7AC]/50">
            <div class="relative overflow-hidden">
              <img src="{{ asset('images/' . $site['image']) }}" alt="{{ $site['title'] }}" class="w-full h-48 md:h-56 object-cover group-hover:scale-110 transition-transform duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="w-8 h-8 bg-[#23C7AC] rounded-full flex items-center justify-center">
                  <svg class="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14"></path>
                  </svg>
                </div>
              </div>
            </div>
            <div class="p-4 md:p-6">
              <h4 class="text-lg md:text-xl font-bold text-white group-hover:text-[#23C7AC] transition-colors duration-300">{{ $site['title'] }}</h4>
              <p class="text-gray-400 text-sm mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">Click to visit website</p>
            </div>
          </a>
          @endforeach
      </div>
    </div>


        <div id="designs" class="hidden grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6 md:gap-8" role="tabpanel" aria-labelledby="designs-tab">
        @foreach (['design1.jpg', 'design2.jpg', 'design5.jpg', 'design6.jpg', 'design7.jpg'] as $design)
        <div class="group bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl shadow-lg hover:shadow-2xl overflow-hidden border border-gray-700 hover:border-[#23C7AC]/50 transition-all duration-300 hover:scale-[1.02]">
            <div class="relative overflow-hidden aspect-[4/3]">
              <img src="{{ asset('images/designs/' . $design) }}" alt="Design" class="w-full h-full object-cover group-hover:scale-110 transition-transform duration-500">
              <div class="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              <div class="absolute inset-0 flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                <div class="w-12 h-12 bg-[#23C7AC] rounded-full flex items-center justify-center">
                  <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                  </svg>
                </div>
              </div>
            </div>
        </div>
        @endforeach
    </div>

  <div id="logos" class="hidden grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4 md:gap-6" role="tabpanel" aria-labelledby="logos-tab">
    @foreach (['design3.jpg', 'design4.jpg', 'design1.jpg', 'design2.jpg', 'design5.jpg', 'design6.jpg', 'design7.jpg', 'design8.jpg'] as $design)
    <div class="group bg-gradient-to-br from-gray-800 to-gray-900 rounded-2xl shadow-lg hover:shadow-2xl overflow-hidden aspect-square border border-gray-700 hover:border-[#23C7AC]/50 transition-all duration-300 hover:scale-[1.02]">
        <div class="relative w-full h-full p-3 md:p-4">
          <img src="{{ asset('images/logos/' . $design) }}"
               alt="Logo Design"
               class="w-full h-full object-contain group-hover:scale-110 transition-transform duration-300" />
          <div class="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-2xl"></div>
        </div>
    </div>
    @endforeach
</div>






    </div>
  </section>

<script>
// Enhanced tab switching with animations
document.addEventListener('DOMContentLoaded', function() {
    const tabs = document.querySelectorAll('[role="tab"]');
    const tabContents = document.querySelectorAll('[role="tabpanel"]');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetId = this.getAttribute('data-tabs-target');
            const targetContent = document.querySelector(targetId);

            // Remove active classes from all tabs
            tabs.forEach(t => {
                t.classList.remove('bg-gradient-to-r', 'from-[#23C7AC]', 'to-[#1ea892]', 'active-tab');
                t.classList.add('bg-gray-800', 'border', 'border-gray-700', 'inactive-tab');
            });

            // Add active class to clicked tab
            this.classList.remove('bg-gray-800', 'border', 'border-gray-700', 'inactive-tab');
            this.classList.add('bg-gradient-to-r', 'from-[#23C7AC]', 'to-[#1ea892]', 'active-tab');

            // Hide all tab contents with animation
            tabContents.forEach(content => {
                content.classList.add('hidden');
                content.classList.remove('animate-slide-up');
            });

            // Show target content with animation
            if (targetContent) {
                setTimeout(() => {
                    targetContent.classList.remove('hidden');
                    targetContent.classList.add('animate-slide-up');
                }, 150);
            }
        });
    });

    // Smooth scroll for navigation links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
});
</script>

<x-footer-main />
