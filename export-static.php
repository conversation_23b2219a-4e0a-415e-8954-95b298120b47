<?php
// Simple script to export your Laravel views to static HTML
// Run this in your Laravel project root: php export-static.php

require_once 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$kernel = $app->make(Illuminate\Contracts\Http\Kernel::class);

// Create output directory
if (!file_exists('static-export')) {
    mkdir('static-export', 0755, true);
}

// Copy public assets
exec('cp -r public/* static-export/');

// Render the main page
$request = Illuminate\Http\Request::create('/', 'GET');
$response = $kernel->handle($request);

// Save the rendered HTML
file_put_contents('static-export/index.html', $response->getContent());

echo "Static files exported to 'static-export' directory\n";
echo "You can now deploy the 'static-export' folder to Vercel\n";
?>
