<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EHIGOLD - Creative Designer & Developer</title>
    <meta name="description" content="Creative Graphics and Web Designer with over 5 years of experience. I design stunning logos and flyers, teach web design, and develop fullstack web applications.">
    <meta name="keywords" content="logo design, flyer design, web development, graphics design, Port Harcourt designer">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Custom Styles -->
    <style>
      @keyframes float-slow {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-10px) rotate(5deg); }
      }

      @keyframes float-fast {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-20px) rotate(-5deg); }
      }

      @keyframes float-medium {
        0%, 100% { transform: translateY(0px) rotate(0deg); }
        50% { transform: translateY(-15px) rotate(3deg); }
      }

      @keyframes gradient-shift {
        0%, 100% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
      }

      @keyframes glow-pulse {
        0%, 100% { box-shadow: 0 0 20px rgba(35, 199, 172, 0.3); }
        50% { box-shadow: 0 0 40px rgba(35, 199, 172, 0.6); }
      }

      @keyframes slide-in-up {
        from {
          opacity: 0;
          transform: translateY(30px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      .animate-float-slow {
        animation: float-slow 6s ease-in-out infinite;
      }

      .animate-float-fast {
        animation: float-fast 3s ease-in-out infinite;
      }

      .animate-float-medium {
        animation: float-medium 4s ease-in-out infinite;
      }

      .animate-gradient {
        background-size: 200% 200%;
        animation: gradient-shift 3s ease infinite;
      }

      .animate-glow {
        animation: glow-pulse 2s ease-in-out infinite;
      }

      .animate-slide-up {
        animation: slide-in-up 0.6s ease-out;
      }

      /* Smooth scrolling */
      html {
        scroll-behavior: smooth;
      }

      /* Custom scrollbar */
      ::-webkit-scrollbar {
        width: 8px;
      }

      ::-webkit-scrollbar-track {
        background: #1f2937;
      }

      ::-webkit-scrollbar-thumb {
        background: #23C7AC;
        border-radius: 4px;
      }

      ::-webkit-scrollbar-thumb:hover {
        background: #1ea892;
      }

      /* Tab switching animations */
      .tab-content {
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease-in-out;
      }

      .tab-content.active {
        opacity: 1;
        transform: translateY(0);
      }

      /* Prevent tab content flashing */
      [role="tabpanel"] {
        transition: opacity 0.3s ease-out, transform 0.3s ease-out;
        will-change: opacity, transform;
      }

      [role="tabpanel"].hidden {
        display: none !important;
        visibility: hidden;
      }

      [role="tabpanel"]:not(.hidden) {
        display: grid;
        visibility: visible;
      }

      /* Tab button states */
      [role="tab"] {
        transition: all 0.2s ease-in-out;
        will-change: background, border-color;
      }

      /* Prevent any layout shifts during tab switching */
      #tabsContent {
        position: relative;
        min-height: 400px;
      }

      /* Ensure smooth rendering */
      .portfolio-container {
        transform: translateZ(0);
        backface-visibility: hidden;
      }

      /* Modal/Lightbox styles */
      .image-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 0;
        visibility: hidden;
        transition: all 0.3s ease-in-out;
      }

      .image-modal.active {
        opacity: 1;
        visibility: visible;
      }

      .image-modal img {
        max-width: 90%;
        max-height: 90%;
        object-fit: contain;
        border-radius: 12px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.5);
        transform: scale(0.8);
        transition: transform 0.3s ease-out;
      }

      .image-modal.active img {
        transform: scale(1);
      }

      .image-modal .close-btn {
        position: absolute;
        top: 20px;
        right: 20px;
        width: 40px;
        height: 40px;
        background: rgba(255, 255, 255, 0.1);
        border: 2px solid rgba(255, 255, 255, 0.3);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
      }

      .image-modal .close-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        border-color: rgba(255, 255, 255, 0.5);
        transform: scale(1.1);
      }

      .image-modal .close-btn svg {
        width: 20px;
        height: 20px;
        color: white;
      }

      /* Mobile responsive improvements */
      @media (max-width: 640px) {
        .hero-text {
          font-size: clamp(2rem, 8vw, 3rem);
        }
        
        .section-padding {
          padding-left: 1rem;
          padding-right: 1rem;
        }
      }
    </style>
</head>
<body class="bg-black text-white">
  <!-- Hero Section -->
  <section class="relative z-10 pt-16 pb-16 md:pt-20 md:pb-20 bg-gradient-to-br from-black via-gray-900 to-gray-800 text-white overflow-hidden min-h-screen flex items-center">
    <!-- Enhanced Floating Geometric Shapes -->
    <div class="absolute inset-0 z-0 pointer-events-none">
      <!-- Floating Circle -->
      <div class="absolute top-10 left-4 md:left-10 w-6 h-6 md:w-10 md:h-10 bg-[#23C7AC] opacity-20 rounded-full animate-pulse"></div>
      
      <!-- Floating Square -->
      <div class="absolute bottom-20 right-4 md:right-20 w-4 h-4 md:w-8 md:h-8 bg-[#23C7AC] opacity-20 rotate-12 animate-bounce"></div>
      
      <!-- Additional floating elements for visual appeal -->
      <div class="absolute top-1/4 left-1/4 w-3 h-3 md:w-6 md:h-6 bg-[#23C7AC] opacity-10 rounded-full animate-ping"></div>
      <div class="absolute bottom-1/3 left-1/3 w-2 h-2 md:w-4 md:h-4 bg-white opacity-10 rounded-full animate-pulse"></div>

      <!-- Floating Triangle (SVG) -->
      <svg class="absolute top-1/3 right-1/4 md:right-1/3 w-8 h-8 md:w-12 md:h-12 opacity-20 animate-pulse" viewBox="0 0 100 100" fill="#23C7AC">
        <polygon points="50,0 0,100 100,100" />
      </svg>
    </div>

    <!-- Hero Content -->
    <div class="relative container mx-auto flex flex-col md:flex-row items-center md:items-center gap-8 md:gap-12 px-4 md:px-6 z-10 w-full">
      <!-- Profile Image -->
      <div class="flex-shrink-0 order-1 md:order-1">
        <div class="relative">
          <img src="images/ehigoldd.jpg" alt="Profile Picture" class="w-48 h-48 md:w-64 md:h-64 lg:w-72 lg:h-72 rounded-3xl shadow-2xl border-4 border-[#23C7AC] object-cover transform hover:scale-105 transition-transform duration-300" />
          <!-- Glow effect -->
          <div class="absolute inset-0 rounded-3xl bg-[#23C7AC] opacity-20 blur-xl -z-10"></div>
        </div>
      </div>

      <!-- Bio & Text -->
      <div class="flex flex-col justify-center text-center md:text-left order-2 md:order-2 space-y-4 md:space-y-6 max-w-2xl">
        <h1 class="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-bold leading-tight">
          Hi, I'm <span class="text-[#23C7AC] bg-gradient-to-r from-[#23C7AC] to-[#1ea892] bg-clip-text text-transparent">EHIGOLD</span>
        </h1>

        <p class="text-base sm:text-lg md:text-xl lg:text-2xl text-gray-300 leading-relaxed max-w-xl mx-auto md:mx-0">
          I design stunning logos and flyers. I also teach web design, and develop fullstack web applications that bring ideas to life.
        </p>

        <!-- Enhanced CTA Buttons -->
        <div class="flex flex-col sm:flex-row gap-4 mt-6 md:mt-8">
          <a href="#portfolio" class="bg-[#23C7AC] hover:bg-[#1ea892] text-white px-6 py-3 rounded-full font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl">
            View My Work
          </a>
          <a href="#contact" class="border-2 border-[#23C7AC] text-[#23C7AC] hover:bg-[#23C7AC] hover:text-white px-6 py-3 rounded-full font-semibold transition-all duration-300">
            Get In Touch
          </a>
        </div>

        <!-- Scroll Down -->
        <div class="mt-8 md:mt-12">
          <a href="#overview" class="animate-bounce text-gray-400 hover:text-[#23C7AC] inline-flex items-center gap-2 transition-colors duration-300">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 md:h-8 md:w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
            </svg>
            <span class="text-sm md:text-base">Scroll down</span>
          </a>
        </div>
      </div>
    </div>
  </section>

  <!-- Overview Section -->
  <section id="overview" class="py-16 md:py-24 px-4 md:px-6 lg:px-32 bg-gradient-to-br from-gray-900 via-gray-800 to-black text-white">
    <div class="max-w-7xl mx-auto">
      <div class="text-center mb-12 md:mb-16">
        <h2 class="text-3xl md:text-4xl lg:text-5xl font-bold mb-6 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">Overview</h2>
        <p class="text-gray-300 text-base md:text-lg lg:text-xl mb-10 leading-relaxed max-w-4xl mx-auto">
          I'm a creative Graphics and Web Designer with over 5 years of experience delivering visually
          compelling and user-focused designs. With an additional year of hands-on experience as a
          Web Developer, I bridge the gap between design and functionality — building responsive,
          full-featured websites and applications that not only look great but also perform flawlessly.
        </p>
      </div>

      <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 md:gap-8">
        <!-- Card 1 -->
        <div class="group h-64 md:h-72 bg-gradient-to-br from-gray-800 to-gray-900 border border-[#23C7AC]/30 hover:border-[#23C7AC] hover:bg-gradient-to-br hover:from-gray-700 hover:to-gray-800 transition-all duration-500 rounded-2xl p-6 shadow-lg hover:shadow-2xl text-center flex flex-col items-center justify-center transform hover:-translate-y-2">
          <div class="text-[#23C7AC] mb-4 group-hover:scale-110 transition-transform duration-300">
            <!-- Icon: Paint Brush for Graphics -->
            <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-[#23C7AC] to-[#1ea892] rounded-2xl flex items-center justify-center mx-auto shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 md:w-10 md:h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15.232 5.232a2.828 2.828 0 114 4L7 21H3v-4L15.232 5.232z" />
              </svg>
            </div>
          </div>
          <h3 class="text-xl md:text-2xl font-bold text-white group-hover:text-[#23C7AC] transition-colors duration-300">Graphics Designer</h3>
          <p class="text-gray-400 text-sm md:text-base mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">Creative visual solutions</p>
        </div>

        <!-- Card 2 -->
        <div class="group h-64 md:h-72 bg-gradient-to-br from-gray-800 to-gray-900 border border-[#23C7AC]/30 hover:border-[#23C7AC] hover:bg-gradient-to-br hover:from-gray-700 hover:to-gray-800 transition-all duration-500 rounded-2xl p-6 shadow-lg hover:shadow-2xl text-center flex flex-col items-center justify-center transform hover:-translate-y-2">
          <div class="text-[#23C7AC] mb-4 group-hover:scale-110 transition-transform duration-300">
            <!-- Icon: Layout for Web Design -->
            <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-[#23C7AC] to-[#1ea892] rounded-2xl flex items-center justify-center mx-auto shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 md:w-10 md:h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v3H3V4zm0 5h18v11a1 1 0 01-1 1H4a1 1 0 01-1-1V9z" />
              </svg>
            </div>
          </div>
          <h3 class="text-xl md:text-2xl font-bold text-white group-hover:text-[#23C7AC] transition-colors duration-300">Web Designer</h3>
          <p class="text-gray-400 text-sm md:text-base mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">User-focused interfaces</p>
        </div>

        <!-- Card 3 -->
        <div class="group h-64 md:h-72 bg-gradient-to-br from-gray-800 to-gray-900 border border-[#23C7AC]/30 hover:border-[#23C7AC] hover:bg-gradient-to-br hover:from-gray-700 hover:to-gray-800 transition-all duration-500 rounded-2xl p-6 shadow-lg hover:shadow-2xl text-center flex flex-col items-center justify-center transform hover:-translate-y-2">
          <div class="text-[#23C7AC] mb-4 group-hover:scale-110 transition-transform duration-300">
            <!-- Icon: Terminal/Code for Web Developer -->
            <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-[#23C7AC] to-[#1ea892] rounded-2xl flex items-center justify-center mx-auto shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 md:w-10 md:h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 9l-3 3 3 3m5-6h3m4 6H5a2 2 0 01-2-2V7a2 2 0 012-2h16a2 2 0 012 2v8a2 2 0 01-2 2z" />
              </svg>
            </div>
          </div>
          <h3 class="text-xl md:text-2xl font-bold text-white group-hover:text-[#23C7AC] transition-colors duration-300">Web Developer</h3>
          <p class="text-gray-400 text-sm md:text-base mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">Full-stack solutions</p>
        </div>

        <!-- Card 4 -->
        <div class="group h-64 md:h-72 bg-gradient-to-br from-gray-800 to-gray-900 border border-[#23C7AC]/30 hover:border-[#23C7AC] hover:bg-gradient-to-br hover:from-gray-700 hover:to-gray-800 transition-all duration-500 rounded-2xl p-6 shadow-lg hover:shadow-2xl text-center flex flex-col items-center justify-center transform hover:-translate-y-2">
          <div class="text-[#23C7AC] mb-4 group-hover:scale-110 transition-transform duration-300">
            <!-- Icon: Teaching -->
            <div class="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-[#23C7AC] to-[#1ea892] rounded-2xl flex items-center justify-center mx-auto shadow-lg">
              <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8 md:w-10 md:h-10 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
          </div>
          <h3 class="text-xl md:text-2xl font-bold text-white group-hover:text-[#23C7AC] transition-colors duration-300">Web Design Tutor</h3>
          <p class="text-gray-400 text-sm md:text-base mt-2 opacity-0 group-hover:opacity-100 transition-opacity duration-300">Knowledge sharing</p>
        </div>
      </div>
    </div>
  </section>
