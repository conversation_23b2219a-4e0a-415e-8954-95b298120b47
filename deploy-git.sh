#!/bin/bash

# EHIGOLD Portfolio - Git Deployment Script
echo "🚀 Preparing EHIGOLD Portfolio for Git deployment..."

# Create a new git repository for the static site
cd static-export

# Initialize git if not already done
if [ ! -d ".git" ]; then
    git init
    echo "✅ Git repository initialized"
fi

# Create .gitignore for static site
cat > .gitignore << EOF
# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Editor files
*.swp
*.swo
*~

# Logs
*.log
EOF

# Add all files
git add .

# Commit
git commit -m "🎨 EHIGOLD Portfolio - Complete static website

✅ Features:
- Fully responsive design
- Interactive portfolio tabs
- Image modal lightbox
- Contact information
- Social media links
- Professional animations

🎯 Ready for Vercel deployment!"

echo "✅ Files committed to git"
echo ""
echo "🔗 Next steps:"
echo "1. Create a new repository on GitHub"
echo "2. Add remote: git remote add origin https://github.com/yourusername/ehigold-portfolio.git"
echo "3. Push: git push -u origin main"
echo "4. Connect to Vercel from GitHub"
echo ""
echo "🌟 Your portfolio is ready for deployment!"
EOF
